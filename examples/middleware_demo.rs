use axum::{
    extract::Query,
    http::StatusCode,
    middleware,
    response::<PERSON><PERSON>,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::net::TcpListener;
use tracing::{info, Level};
use tracing_subscriber;

// 引入我们的中间件
use kuai_saver::middleware::{request_response_logger, simple_request_logger};

#[derive(Deserialize)]
struct QueryParams {
    name: Option<String>,
    age: Option<u32>,
}

#[derive(Serialize, Deserialize)]
struct User {
    id: u32,
    name: String,
    email: String,
}

#[derive(Deserialize)]
struct CreateUserRequest {
    name: String,
    email: String,
}

// 简单的GET处理器
async fn hello() -> &'static str {
    "Hello, World!"
}

// 带查询参数的GET处理器
async fn hello_with_params(Query(params): Query<QueryParams>) -> String {
    match (params.name, params.age) {
        (Some(name), Some(age)) => format!("Hello, {}! You are {} years old.", name, age),
        (Some(name), None) => format!("Hello, {}!", name),
        (None, Some(age)) => format!("Hello! You are {} years old.", age),
        (None, None) => "Hello, anonymous!".to_string(),
    }
}

// 返回JSON的GET处理器
async fn get_user() -> Json<User> {
    Json(User {
        id: 1,
        name: "张三".to_string(),
        email: "<EMAIL>".to_string(),
    })
}

// POST处理器
async fn create_user(Json(payload): Json<CreateUserRequest>) -> (StatusCode, Json<User>) {
    let user = User {
        id: 42,
        name: payload.name,
        email: payload.email,
    };
    
    (StatusCode::CREATED, Json(user))
}

// 模拟慢请求
async fn slow_endpoint() -> &'static str {
    tokio::time::sleep(tokio::time::Duration::from_millis(1200)).await;
    "This was a slow request!"
}

// 模拟错误
async fn error_endpoint() -> StatusCode {
    StatusCode::INTERNAL_SERVER_ERROR
}

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_target(false)
        .init();

    // 创建路由器并应用中间件
    let app = Router::new()
        .route("/", get(hello))
        .route("/hello", get(hello_with_params))
        .route("/user", get(get_user).post(create_user))
        .route("/slow", get(slow_endpoint))
        .route("/error", get(error_endpoint))
        // 应用详细的请求响应日志中间件
        .layer(middleware::from_fn(request_response_logger));
        
    // 你也可以使用简化版本的中间件：
    // .layer(middleware::from_fn(simple_request_logger));

    let listener = TcpListener::bind("0.0.0.0:3000").await.unwrap();
    
    info!("🚀 服务器启动在 http://0.0.0.0:3000");
    info!("📝 测试端点:");
    info!("  GET  /           - 简单问候");
    info!("  GET  /hello?name=张三&age=25 - 带参数问候");
    info!("  GET  /user       - 获取用户JSON");
    info!("  POST /user       - 创建用户 (需要JSON body)");
    info!("  GET  /slow       - 慢请求 (1.2秒)");
    info!("  GET  /error      - 错误响应");
    
    axum::serve(listener, app).await.unwrap();
}
