use std::sync::{Arc, Mutex};

use axum::{middleware, Router, routing::get};
use sea_orm::DatabaseConnection;

use crate::{
    configuration::ApplicationSettings,
    handlers::wx::{handle_wechat_message, verify_signature},
    middleware::request_response_logger,
};
mod wx;

#[derive(Clone)]
pub struct AppState {
    pub http_client: reqwest::Client,
    pub inner: Arc<Mutex<AppStateInner>>,
}

#[derive(Clone)]
pub struct AppStateInner {
    #[allow(dead_code)]
    pub connection_pool: DatabaseConnection,
    pub app_settings: ApplicationSettings,
}

impl AppState {
    pub fn new(pool: DatabaseConnection, app_settings: ApplicationSettings) -> Self {
        let inner = AppStateInner {
            connection_pool: pool,
            app_settings,
        };
        Self {
            http_client: reqwest::Client::new(),
            inner: Arc::new(Mutex::new(inner)),
        }
    }
}

pub fn get_router(state: AppState) -> Router {
    Router::new()
        .route("/ping", get(|| async { "pong" }))
        .route("/wx", get(verify_signature).post(handle_wechat_message))
        .layer(middleware::from_fn(request_response_logger))
        .with_state(state)
}
