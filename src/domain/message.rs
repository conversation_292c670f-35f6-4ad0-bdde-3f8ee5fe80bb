use quick_xml::SeError;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum MsgType {
    Text,
    Image,
    Voice,
    Video,
    ShortVideo,
    Location,
    Link,
    Event,
    #[serde(other)]
    Unknown, // 捕获未知类型，避免解析失败
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "PascalCase")]
pub struct Message {
    pub to_user_name: String,
    pub from_user_name: String,
    #[allow(dead_code)]
    pub create_time: i64,
    pub msg_type: MsgType,
    pub content: Option<String>,
    #[allow(dead_code)]
    pub msg_id: i64,
}

#[derive(Serialize, Debug)]
#[serde(rename_all = "PascalCase")]
pub struct TextMsg {
    pub to_user_name: String,
    pub from_user_name: String,
    pub create_time: i64,
    pub msg_type: MsgType,
    pub content: Option<String>,
}
impl Message {
    pub fn reply_text_msg(&self, content: &str) -> Result<String, SeError> {
        let msg = TextMsg {
            to_user_name: self.from_user_name.clone(),
            from_user_name: self.to_user_name.clone(),
            create_time: chrono::Local::now().timestamp(),
            msg_type: MsgType::Text,
            content: Some(content.to_string()),
        };
        quick_xml::se::to_string_with_root("xml", &msg)
    }
}
