use std::time::Instant;

use axum::{
    extract::{MatchedPath, Request},
    http::{HeaderMap, Method, StatusCode, Uri},
    middleware::Next,
    response::Response,
};
use tracing::{info, warn};

/// 请求响应日志中间件
pub async fn request_response_logger(
    req: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    
    // 提取请求信息
    let method = req.method().clone();
    let uri = req.uri().clone();
    let headers = req.headers().clone();
    let matched_path = req.extensions().get::<MatchedPath>()
        .map(|mp| mp.as_str().to_string())
        .unwrap_or_else(|| uri.path().to_string());
    
    // 记录请求信息
    let request_id = uuid::Uuid::new_v4().to_string();
    let request_span = tracing::info_span!(
        "http_request",
        request_id = %request_id,
        method = %method,
        path = %matched_path,
        uri = %uri,
    );
    
    let _enter = request_span.enter();
    
    // 记录请求详情
    log_request_details(&method, &uri, &headers, &request_id);
    
    // 执行请求
    let response = next.run(req).await;
    
    // 计算处理时间
    let duration = start_time.elapsed();
    let status = response.status();
    
    // 记录响应信息
    log_response_details(status, duration, &request_id);
    
    response
}

/// 记录请求详细信息
fn log_request_details(method: &Method, uri: &Uri, headers: &HeaderMap, request_id: &str) {
    info!(
        request_id = request_id,
        "📥 收到请求: {} {}",
        method,
        uri
    );
    
    // 记录查询参数
    if let Some(query) = uri.query() {
        info!(
            request_id = request_id,
            "🔍 查询参数: {}",
            query
        );
    }
    
    // 记录重要的请求头
    let important_headers = [
        "content-type",
        "content-length",
        "user-agent",
        "authorization",
        "x-forwarded-for",
        "x-real-ip",
        "host",
    ];
    
    for header_name in important_headers {
        if let Some(header_value) = headers.get(header_name) {
            let value = if header_name == "authorization" {
                // 隐藏敏感信息
                "***"
            } else {
                header_value.to_str().unwrap_or("无效的UTF-8")
            };
            
            info!(
                request_id = request_id,
                "📋 请求头 {}: {}",
                header_name,
                value
            );
        }
    }
}

/// 记录响应详细信息
fn log_response_details(status: StatusCode, duration: std::time::Duration, request_id: &str) {
    let duration_ms = duration.as_millis();
    
    let (emoji, level) = match status.as_u16() {
        200..=299 => ("✅", "info"),
        300..=399 => ("🔄", "info"),
        400..=499 => ("⚠️", "warn"),
        500..=599 => ("❌", "error"),
        _ => ("❓", "info"),
    };
    
    match level {
        "warn" => warn!(
            request_id = request_id,
            status = %status,
            duration_ms = duration_ms,
            "{} 响应完成: {} (耗时: {}ms)",
            emoji,
            status,
            duration_ms
        ),
        "error" => tracing::error!(
            request_id = request_id,
            status = %status,
            duration_ms = duration_ms,
            "{} 响应完成: {} (耗时: {}ms)",
            emoji,
            status,
            duration_ms
        ),
        _ => info!(
            request_id = request_id,
            status = %status,
            duration_ms = duration_ms,
            "{} 响应完成: {} (耗时: {}ms)",
            emoji,
            status,
            duration_ms
        ),
    }
    
    // 性能警告
    if duration_ms > 1000 {
        warn!(
            request_id = request_id,
            duration_ms = duration_ms,
            "🐌 慢请求警告: 请求处理时间超过1秒"
        );
    } else if duration_ms > 500 {
        warn!(
            request_id = request_id,
            duration_ms = duration_ms,
            "⏰ 性能提醒: 请求处理时间超过500ms"
        );
    }
}

/// 简化版本的日志中间件（如果不需要详细信息）
pub async fn simple_request_logger(
    req: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let method = req.method().clone();
    let uri = req.uri().clone();
    
    let response = next.run(req).await;
    
    let duration = start_time.elapsed();
    let status = response.status();
    
    info!(
        method = %method,
        uri = %uri,
        status = %status,
        duration_ms = duration.as_millis(),
        "{} {} -> {} ({}ms)",
        method,
        uri.path(),
        status,
        duration.as_millis()
    );
    
    response
}
