#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum Platform {
    Pdd,
    Jd,
    Unknown,
}

impl Platform {
    pub fn from_url(url: &str) -> Self {
        if url.contains("pinduoduo.com") || url.contains("yangkeduo.com") || url.contains("pdd.com")
        {
            Self::Pdd
        } else if url.contains("jd.com") || url.contains("360buy.com") {
            Self::Jd
        } else {
            Self::Unknown
        }
    }
}
