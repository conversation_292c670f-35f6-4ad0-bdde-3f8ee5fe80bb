use crate::{
    domain::{Message, MsgType},
    error::AppResult,
    handlers::AppState,
    service::translate::{TranslateService, identify_platform},
};

pub struct MessageService {
    #[allow(unused)]
    state: AppState,
    translate_service: TranslateService,
}

impl MessageService {
    pub fn new(state: AppState) -> Self {
        let translate_service = TranslateService::new(state.clone());
        Self {
            state,
            translate_service,
        }
    }

    pub async fn handle_message(&self, msg: Message) -> AppResult<String> {
        match msg.msg_type {
            MsgType::Text => self.handle_text_message(msg).await,
            _ => Ok(msg.reply_text_msg("暂不支持此类型消息")?),
        }
    }

    async fn handle_text_message(&self, msg: Message) -> AppResult<String> {
        let content = msg.content.clone().unwrap_or_default();

        if content.contains("http") {
            // 从消息内容中提取商品链接
            if let Some(url) = self.extract_url(&content) {
                // 验证提取的URL是否包含我们支持的平台
                if identify_platform(&url).is_some() {
                    let goods_data = self.translate_service.translate_link(&url).await?;
                    Ok(msg.reply_text_msg(&goods_data.to_string())?)
                } else {
                    Ok(msg.reply_text_msg("暂不支持该平台，目前仅支持拼多多链接")?)
                }
            } else {
                Ok(msg.reply_text_msg("未找到有效的商品链接")?)
            }
        } else {
            Ok(msg.reply_text_msg("请发送商品链接")?)
        }
    }

    /// 从文本中提取URL
    fn extract_url(&self, content: &str) -> Option<String> {
        // 使用正则表达式匹配HTTP/HTTPS链接
        // 匹配模式：http(s)://开头，后面跟非空白字符，直到遇到空白字符或常见的结束符号
        let url_pattern = regex::Regex::new(r"https?://[^\s\u3002\uff0c\uff01\uff1f\uff1b\uff1a\u201c\u201d\u2018\u2019\u300a\u300b\u3008\u3009\u3010\u3011\u300e\u300f\u300c\u300d\ufe43\ufe44\ufe47\ufe48\ufe4f\ufe34\ufe39\ufe3a\ufe17\ufe18\ufe19\ufe35]+").ok()?;

        // 查找所有匹配的URL
        for matched in url_pattern.find_iter(content) {
            let url = matched.as_str();
            // 清理URL末尾可能的标点符号
            let cleaned_url = url.trim_end_matches(&['.', ',', '!', '?', ';', ':', '"', '\'', ')', ']', '}'][..]);

            // 验证这是否是一个商品链接（包含我们支持的平台域名）
            if identify_platform(cleaned_url).is_some() {
                return Some(cleaned_url.to_string());
            }
        }

        None
    }
}
