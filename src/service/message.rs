use linkify::{<PERSON><PERSON><PERSON>, <PERSON>K<PERSON>};

use crate::{
    domain::{Message, MsgType},
    error::AppResult,
    handlers::AppState,
    service::translate::{TranslateService, identify_platform},
};

pub struct MessageService {
    #[allow(unused)]
    state: AppState,
    translate_service: TranslateService,
}

impl MessageService {
    pub fn new(state: AppState) -> Self {
        let translate_service = TranslateService::new(state.clone());
        Self {
            state,
            translate_service,
        }
    }

    pub async fn handle_message(&self, msg: Message) -> AppResult<String> {
        match msg.msg_type {
            MsgType::Text => self.handle_text_message(msg).await,
            _ => Ok(msg.reply_text_msg("暂不支持此类型消息")?),
        }
    }

    async fn handle_text_message(&self, msg: Message) -> AppResult<String> {
        let content = msg.content.clone().unwrap_or_default();

        let response = match self.extract_url(&content) {
            Some(url) => {
                let goods_data = self.translate_service.translate_link(&url).await?;
                goods_data.to_string()
            }
            None if content.contains("http") => "暂不支持该平台，目前仅支持拼多多链接".to_string(),
            None => "请发送商品链接".to_string(),
        };

        Ok(msg.reply_text_msg(&response)?)
    }

    fn extract_url(&self, content: &str) -> Option<String> {
        LinkFinder::new()
            .links(content)
            .filter(|link| link.kind() == &LinkKind::Url)
            .map(|link| link.as_str())
            .find(|url| identify_platform(url).is_some())
            .map(|url| url.to_string())
    }
}
