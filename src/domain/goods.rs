use std::fmt::{self, Display};

use serde::Serialize;

#[derive(Serialize, Default, Debug)]
pub struct GoodsData {
    pub title: String,           // 商品标题
    pub shop_name: String,       // 店铺名称
    pub goods_id: String,        // 商品ID
    pub price: String,           // 折后价
    pub main_pic: String,        // 主图
    pub commission_rate: String, // 佣金
    pub prom_url: String,        // 推广链接
    pub coupon_info: CouponInfo, // 优惠券信息
    pub has_coupon: bool,        // 是否有券
}

#[derive(Serialize, Default, Debug)]
pub struct CouponInfo {
    pub coupon_start_time: String, // 优惠券开始使用时间
    pub coupon_end_time: String,   // 优惠券结束使用时间
    pub coupon_remain_count: i64,  // 优惠券余量
    pub coupon_total_count: i64,   // 优惠券总量
    pub coupon_info: String,
    pub coupon_amount: f64,    // 优惠券面额
    pub coupon_start_fee: f64, // 优惠券起用条件
}

impl Display for GoodsData {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{}\n隐藏优惠券: {}\n券后单价: {}\n预计收益: {}\n\n点击下方链接直接购买: {}",
            self.title,
            self.coupon_info.coupon_amount,
            self.price,
            self.commission_rate,
            self.prom_url
        )
    }
}
