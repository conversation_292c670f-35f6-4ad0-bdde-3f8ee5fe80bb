[package]
name = "kuai_saver"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
async-trait = "0.1.88"
axum = { version = "0.8.4", features = ["macros"] }
chrono = "0.4.41"
config = "0.15.11"
derive_builder = "0.20.2"
hex = "0.4.3"
http-body-util = "0.1.3"
linkify = "0.10.0"
md5 = "0.8.0"
quick-xml = { version = "0.38.0", features = ["serialize"] }
reqwest = { version = "0.12.20", features = ["json", "rustls-tls"] }
sea-orm = { version = "1.1.12", features = [
    "runtime-tokio-rustls",
    "sqlx-postgres",
] }
secrecy = { version = "0.10.3", features = ["serde"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
sha1 = "0.10.6"
strum = { version = "0.27.1", features = ["derive"] }
thiserror = "2.0.12"
tokio = { version = "1.45.1", features = ["macros", "rt-multi-thread"] }
tracing = { version = "0.1.41" }
tracing-appender = "0.2.3"
tracing-error = "0.2.1"
tracing-log = "0.2.0"
tracing-subscriber = { version = "0.3.19", features = [
    "ansi",
    "chrono",
    "env-filter",
    "json",
] }
uuid = "1.17.0"

[dev-dependencies]
thiserror = "2.0.12"
