use crate::{
    domain::{Message, MsgType},
    error::AppResult,
    handlers::AppState,
    service::translate::{TranslateService, identify_platform},
};

pub struct MessageService {
    #[allow(unused)]
    state: AppState,
    translate_service: TranslateService,
}

impl MessageService {
    pub fn new(state: AppState) -> Self {
        let translate_service = TranslateService::new(state.clone());
        Self {
            state,
            translate_service,
        }
    }

    pub async fn handle_message(&self, msg: Message) -> AppResult<String> {
        match msg.msg_type {
            MsgType::Text => self.handle_text_message(msg).await,
            _ => Ok(msg.reply_text_msg("暂不支持此类型消息")?),
        }
    }

    async fn handle_text_message(&self, msg: Message) -> AppResult<String> {
        let content = msg.content.clone().unwrap_or_default();

        if content.contains("http") {
            // 先验证URL是否包含我们支持的平台
            if identify_platform(&content).is_some() {
                let goods_data = self.translate_service.translate_link(&content).await?;
                Ok(msg.reply_text_msg(&goods_data.to_string())?)
            } else {
                Ok(msg.reply_text_msg("暂不支持该平台，目前仅支持拼多多链接")?)
            }
        } else {
            Ok(msg.reply_text_msg("请发送商品链接")?)
        }
    }
}
