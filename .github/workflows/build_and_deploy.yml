name: Build and Deploy Docker Image

on:
  pull_request:
    branches:
      - main

concurrency:
  group: build-and-deploy-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cache cargo registry
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
        key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}

    - name: Cache cargo build
      uses: actions/cache@v4
      with:
        path: target
        key: ${{ runner.os }}-cargo-build-${{ hashFiles('**/Cargo.lock') }}

    - name: Build docker image locally
      run: docker build -t kuai_saver:latest .

    - name: Save image to tarball
      run: |
        docker save kuai_saver:latest -o kuai_saver.tar
        chmod 644 kuai_saver.tar


    - name: Upload image tar to server
      uses: appleboy/scp-action@v0.1.7
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USER }}
        key: ${{ secrets.SSH_KEY }}
        port: ${{ secrets.PORT }}
        source: kuai_saver.tar
        target: /opt

    - name: Load and run docker on server with status check
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USER }}
        key: ${{ secrets.SSH_KEY }}
        port: ${{ secrets.PORT }}
        script: |
          docker load -i /opt/kuai_saver.tar
          docker stop kuai_saver || true
          docker rm kuai_saver || true
    
          docker run -d --name kuai_saver \
            -e APP_APPLICATION__PDD__CLIENT_ID=${{ secrets.PDD_CLIENT_ID }} \
            -e APP_APPLICATION__PDD__CLIENT_SECRET=${{ secrets.PDD_CLIENT_SECRET }} \
            -e APP_APPLICATION__PDD__PID=${{ secrets.PDD_PID }} \
            -v /var/log/kuai_saver:/opt/kuai_saver/logs \
            -p 80:8000 \
            kuai_saver:latest
    
          # 等待几秒让容器启动
          sleep 5
    
          # 检查容器是否运行
          status=$(docker inspect -f '{{.State.Running}}' kuai_saver)
          if [ "$status" != "true" ]; then
            echo "🚨 容器启动失败！输出最近日志："
            docker logs kuai_saver --tail 20
            exit 1
          else
            echo "✅ 容器运行正常。"
          fi

