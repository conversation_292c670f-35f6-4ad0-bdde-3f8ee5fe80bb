use axum::{
    body::Bytes,
    extract::{Query, State},
};
use serde::Deserialize;
use sha1::{Digest, Sha1};
use tracing::info;

use crate::{
    domain::Message, error::AppResult, handlers::AppState, service::message::MessageService,
};

/// 响应微信验证
#[derive(Deserialize)]
pub struct CodeParams {
    signature: String,
    timestamp: String,
    nonce: String,
    echostr: String,
}

impl CodeParams {
    pub fn generate_signature(&self, token: &str) -> String {
        let x = Sha1::digest(self.get_param(token));
        hex::encode(x)
    }

    fn get_param(&self, token: &str) -> String {
        let mut v = [token, &self.timestamp, &self.nonce];
        v.sort_unstable();
        v.join("")
    }
}

/// 处理授权码回调
pub async fn verify_signature(
    Query(query): Query<CodeParams>,
    State(state): State<AppState>,
) -> AppResult<String> {
    let token = &state.inner.lock().unwrap().app_settings.wechat.token;
    if query.generate_signature(token) == query.signature {
        info!("echostr: {}", query.echostr);
        return Ok(query.echostr.to_string());
    }

    Ok("".to_string())
}

pub async fn handle_wechat_message(
    State(state): State<AppState>,
    body: Bytes,
) -> AppResult<String> {
    let message_service = MessageService::new(state);
    let msg: Message = quick_xml::de::from_reader(body.trim_ascii())?;

    message_service.handle_message(msg).await
}
