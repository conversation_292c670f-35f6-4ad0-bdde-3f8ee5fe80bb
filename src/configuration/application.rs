use secrecy::SecretString;
use serde::Deserialize;

#[derive(Deserialize, <PERSON>lone)]
pub struct ApplicationSettings {
    pub port: u16,
    pub wechat: WechatSettings,
    pub pdd: PddSettings,
    pub jd: JdSettings,
}

#[derive(Deserialize, Clone)]
pub struct WechatSettings {
    pub app_id: String,
    pub app_secret: String,
    pub token: String,
}

#[derive(Deserialize, Clone)]
pub struct PddSettings {
    pub client_id: SecretString,
    pub client_secret: SecretString,
    pub pid: SecretString,
    pub domain: String,
    pub api_good_search: String,
    pub api_gen_short_url: String,
}

#[derive(Deserialize, Clone)]
pub struct JdSettings {
    pub app_key: SecretString,
    pub app_secret: SecretString,
    pub domain: String,
    pub api_good_search: String,
    pub api_gen_short_url: String,
}
