use axum::{
    body::Bytes,
    extract::{Query, State},
};
use quick_xml::de::from_reader;
use serde::Deserialize;
use sha1::{Digest, Sha1};
use tracing_log::log::info;

use crate::{
    domain::{Message, MsgType},
    error::AppResult,
    handlers::AppState,
    service::translate::translate_link,
};

/// 响应微信验证
#[derive(Deserialize)]
pub struct CodeParams {
    signature: String,
    timestamp: String,
    nonce: String,
    echostr: String,
}

impl CodeParams {
    pub fn generate_signature(&self, token: &str) -> String {
        let x = Sha1::digest(self.get_param(token));
        hex::encode(x)
    }

    fn get_param(&self, token: &str) -> String {
        let mut v = [token, &self.timestamp, &self.nonce];
        v.sort_unstable();
        v.join("")
    }
}

/// 处理授权码回调
pub async fn verify_signature(
    Query(query): Query<CodeParams>,
    State(state): State<AppState>,
) -> AppResult<String> {
    let token = &state.inner.lock().unwrap().app_settings.wechat.token;
    if query.generate_signature(token) == query.signature {
        info!("echostr: {}", query.echostr);
        return Ok(query.echostr.to_string());
    }

    Ok("".to_string())
}

pub async fn handle_wechat_message(state: State<AppState>, body: Bytes) -> AppResult<String> {
    let msg: Message = from_reader(body.trim_ascii()).unwrap();
    match msg.msg_type {
        MsgType::Text => {
            if msg.content.is_none() {
                return Ok(msg.reply_text_msg("nihaoya")?);
            }

            let mut response_content = "nihaoya".to_string();
            let content = msg.content.clone().unwrap();
            info!("content: {}", msg.content.clone().unwrap());

            if content.contains("http") {
                let resp = translate_link(&content, state.0).await?;
                response_content = resp.to_string();
            }

            let msg = msg.reply_text_msg(response_content.as_str())?;
            info!("rsp: {}", msg);

            return Ok(msg);
        }
        _ => {
            info!("other: {:?}", msg.msg_type);
        }
    }

    Ok("()".to_string())
}


use axum::{body::Bytes, extract::{Query, State}};
use tracing::info;

use crate::{
    domain::{Message, MsgType},
    error::AppResult,
    handlers::AppState,
    service::message::MessageService,
};

mod signature;
mod types;

pub use signature::*;
pub use types::*;

pub async fn handle_wechat_message(
    State(state): State<AppState>, 
    body: Bytes
) -> AppResult<String> {
    let message_service = MessageService::new(state);
    let msg: Message = quick_xml::de::from_reader(body.trim_ascii())?;
    
    message_service.handle_message(msg).await
}