### 测试中间件日志功能

### 1. 简单的GET请求
GET http://localhost:8080/ping
Accept: application/json

### 2. 微信验证GET请求（带查询参数）
GET http://localhost:8080/wx?signature=test&timestamp=1234567890&nonce=abc123&echostr=hello
Accept: text/plain

### 3. 微信消息POST请求（带XML body）
POST http://localhost:8080/wx
Content-Type: application/xml

<xml>
<ToUserName><![CDATA[toUser]]></ToUserName>
<FromUserName><![CDATA[fromUser]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[hello world]]></Content>
<MsgId>1234567890123456</MsgId>
</xml>

### 4. 微信消息POST请求（包含拼多多链接）
POST http://localhost:8080/wx
Content-Type: application/xml

<xml>
<ToUserName><![CDATA[toUser]]></ToUserName>
<FromUserName><![CDATA[fromUser]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[看看这个商品 https://mobile.yangkeduo.com/goods.html?goods_id=123456 怎么样]]></Content>
<MsgId>1234567890123456</MsgId>
</xml>

### 5. 测试不存在的路径（404错误）
GET http://localhost:8080/nonexistent
Accept: application/json

### 6. 测试POST到只支持GET的端点（405错误）
POST http://localhost:8080/ping
Content-Type: application/json

{
  "test": "data"
}
