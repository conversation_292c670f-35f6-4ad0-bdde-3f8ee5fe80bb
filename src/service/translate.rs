use std::sync::Arc;

use async_trait::async_trait;
use tracing::warn;

use crate::{
    Platform,
    domain::GoodsData,
    error::{TranslateError, TranslateResult},
    handlers::AppState,
    service::translate::pdd::Pdd,
};

pub mod pdd;

#[async_trait]
pub trait Translator: Send + Sync {
    /// 搜索商品信息
    async fn search(&self, url: &str) -> TranslateResult<GoodsData>;

    /// 生成短链接
    async fn gen_short_url(&self, url: &str) -> TranslateResult<String>;
}

/// 转链请求参数
pub async fn translate_link(url: &str, state: AppState) -> TranslateResult<GoodsData> {
    let translator = get_translator(url, &state)?;
    let mut goods_data = translator.search(url).await?;
    goods_data.prom_url = translator.gen_short_url(url).await?;

    Ok(goods_data)
}

/// 根据URL获取适合的转链器
fn get_translator(url: &str, state: &AppState) -> TranslateResult<Arc<dyn Translator>> {
    let platform = identify_platform(url)
        .ok_or_else(|| TranslateError::UnsupportedPlatform("平台暂不支持".to_string()))?;

    match platform {
        Platform::Pdd => {
            let settings = state.inner.lock().unwrap().app_settings.pdd.clone();
            Ok(Arc::new(Pdd::new(settings, state.http_client.clone())))
        }
        Platform::Jd => {
            todo!()
        }
        // 后续可以添加其他平台支持
        Platform::Unknown => {
            warn!("Unkown platform: {:?}", platform);
            Err(TranslateError::UnsupportedPlatform("未知平台".to_string()))
        }
    }
}

pub struct TranslateService {
    state: AppState,
}

impl TranslateService {
    pub fn new(state: AppState) -> Self {
        Self { state }
    }

    pub async fn translate_link(&self, url: &str) -> TranslateResult<GoodsData> {
        translate_link(url, self.state.clone()).await
    }
}

/// 识别链接所属平台
pub fn identify_platform(url: &str) -> Option<Platform> {
    if url.contains("pinduoduo.com") || url.contains("yangkeduo.com") || url.contains("pdd.com") {
        Some(Platform::Pdd)
    } else {
        None
    }
}
