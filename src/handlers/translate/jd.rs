// use std::{
//     collections::{BTreeMap, HashMap},
//     default,
//     fmt::Display,
//     string,
// };

// use anyhow::Result;
// use async_trait::async_trait;
// use chrono::Utc;
// use derive_builder::Builder;
// use reqwest::{Client, StatusCode, header::Values};
// use secrecy::ExposeSecret;
// use serde::{Deserialize, Serialize};
// use serde_json::{Map, Value};

// use crate::{
//     configuration::application::JdSettings,
//     error::{TranslateError, TranslateResult},
//     route::translate::{GoodInfo, Translator},
// };

// #[derive(Debug)]
// pub struct Jd {
//     client: reqwest::Client,
//     domain: String,
//     app_key: String,
//     app_secret: String,
//     api_good_search: String,
//     api_gen_short_url: String,
// }

// impl Jd {
//     pub fn new(settings: JdSettings) -> Self {
//         Self {
//             client: Client::builder().build().unwrap(),
//             domain: settings.domain,
//             app_key: settings.app_key.expose_secret().to_string(),
//             app_secret: settings.app_secret.expose_secret().to_string(),
//             api_good_search: settings.api_good_search,
//             api_gen_short_url: settings.api_gen_short_url,
//         }
//     }
// }

// #[derive(Builder, Clone, Serialize, Deserialize)]
// pub struct SystemParams<'a> {
//     app_key: &'a str,
//     app_secret: &'a str,
//     method: &'a str,      // API 接口名称
//     timestamp: &'a str, // 时间戳，格式为yyyy-MM-dd HH:mm:ss，时区为GMT+8。API服务端允许客户端请求最大时间误差为10分钟
//     format: &'a str,    // 响应格式
//     v: &'a str,         // API协议版本，请根据API具体版本号传入此参数，一般为1.0
//     sign_method: &'a str, // 签名的摘要算法，暂时只支持md5
//     sign: &'a str,      // API输入参数签名结果
//     #[serde(rename = "360buy_param_json")]
//     buy_param_json: &'a str,
// }

// impl Default for SystemParams<'_> {
//     fn default() -> Self {
//         Self {
//             app_key: Default::default(),
//             app_secret: Default::default(),
//             method: Default::default(),
//             timestamp: Default::default(),
//             format: "json",
//             v: "1",
//             sign_method: "md5",
//             sign: Default::default(),
//             buy_param_json: Default::default(),
//         }
//     }
// }

// #[derive(Builder, Clone, Serialize, Deserialize)]
// pub struct GoodsReqDto<'a> {
//     keyword: &'a str,
//     scene_id: SceneId, // 场景ID，支持入参1,2；2需要权限申请
// }

// #[derive(Clone, Serialize, Deserialize)]
// pub enum SceneId {
//     One,
//     Two,
// }

// impl ToString for SceneId {
//     fn to_string(&self) -> String {
//         match self {
//             SceneId::One => "1".to_string(),
//             SceneId::Two => "2".to_string(),
//         }
//     }
// }

// impl Default for GoodsReqDto<'_> {
//     fn default() -> Self {
//         Self {
//             keyword: Default::default(),
//             scene_id: SceneId::One,
//         }
//     }
// }

// #[async_trait]
// impl Translator for Jd {
//     /// 搜索商品信息
//     async fn search(&self, url: &str) -> TranslateResult<GoodInfo> {
//         let business_params = GoodsReqDtoBuilder::default()
//             .keyword(url)
//             .build()
//             .map_err(|e| TranslateError::Internal(e.to_string()))?;

//         let timestamp = Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();
//         let business_params_str = struct_to_string(business_params).unwrap();
//         let mut system_params = SystemParamsBuilder::default()
//             .app_key(&self.app_key)
//             .app_secret(&self.app_secret)
//             .method(&self.api_good_search)
//             .buy_param_json(&business_params_str)
//             .timestamp(&timestamp)
//             .build()
//             .map_err(|e| TranslateError::Internal(e.to_string()))?;

//         let sign = struct_to_string(&system_params).unwrap();
//         system_params.sign = &sign;
//         let res = self
//             .client
//             .get(self.domain.as_str())
//             .query(&system_params)
//             .send()
//             .await
//             .map_err(|e| TranslateError::Request(e.to_string()))?;

//         if res.status() != StatusCode::OK {
//             return Err(TranslateError::Request(res.status().to_string()));
//         }

//         let response = res
//             .json::<GoodsQueryResult>()
//             .await
//             .map_err(|e| TranslateError::Internal(format!("解析响应失败: {}", e)))?;

//         Ok(GoodInfo {
//             activity_promotion_rate: 0,
//             predict_promotion_rate: 0,
//             promotion_rate: 0,
//             coupon_discount: response
//                 .query_result
//                 .data
//                 .first()
//                 .unwrap()
//                 .coupon_info
//                 .coupon_list
//                 .first()
//                 .unwrap()
//                 .discount,
//             coupon_discount_price: 0,
//             origin_price: 0,
//             short_url: "".to_string(),
//         })
//     }

//     /// 生成短链接
//     async fn gen_short_url(&self, url: &str) -> anyhow::Result<String> {
//         todo!()
//     }
// }

// #[derive(Deserialize)]
// pub struct GoodsQueryResult {
//     query_result: QueryResult,
// }

// #[derive(Deserialize)]
// pub struct QueryResult {
//     code: String,
//     message: String,
//     data: Vec<GoodResp>,
// }

// #[derive(Deserialize)]
// pub struct GoodResp {
//     #[serde(rename(deserialize = "skuName"))]
//     sku_name: String,
//     sku_id: String,
//     commission_info: CommissionInfo,
//     coupon_info: CouponInfo,
// }

// #[derive(Deserialize)]
// pub struct CommissionInfo {
//     commission: f64,       // 佣金
//     commission_share: f64, // 佣金比例
//     // coupon_commission: f64, // 券后佣金，（促销价-优惠券面额）*佣金比例
//     plus_commission_share: f64, // plus佣金比例，plus用户购买推广者能获取到的佣金比例
// }

// #[derive(Deserialize)]
// pub struct CouponInfo {
//     coupon_list: Vec<Coupon>,
// }

// #[derive(Deserialize)]
// pub struct Coupon {
//     bind_type: i8, // 券种类 (优惠券种类：0 - 全品类，1 - 限品类（自营商品），2 - 限店铺，3 - 店铺限商品券)
//     discount: i64, // 券面额
//     link: String,  // 券链接
// }

// pub fn struct_to_string<T: Serialize>(value: T) -> anyhow::Result<String> {
//     let value = serde_json::to_value(value)?;
//     if let Value::Object(map) = value {
//         let btree_map = json_map_to_btree_map(&map);
//         serde_json::to_string(&btree_map).map_err(|e| anyhow::anyhow!(e))
//     } else {
//         anyhow::bail!("failed to transform to string")
//     }
// }

// fn json_map_to_btree_map(map: &Map<String, Value>) -> BTreeMap<String, Value> {
//     let mut btree_map = BTreeMap::new();
//     for (k, v) in map {
//         let val = match v {
//             Value::Object(obj) => Value::Object(json_map_to_btree_map(obj).into_iter().collect()),
//             Value::Array(arr) => {
//                 let new_arr: Vec<Value> = arr
//                     .iter()
//                     .map(|item| {
//                         if let Value::Object(obj) = item {
//                             Value::Object(json_map_to_btree_map(obj).into_iter().collect())
//                         } else {
//                             item.clone()
//                         }
//                     })
//                     .collect();
//                 Value::Array(new_arr)
//             }
//             _ => v.clone(),
//         };
//         btree_map.insert(k.clone(), val);
//     }
//     btree_map
// }
