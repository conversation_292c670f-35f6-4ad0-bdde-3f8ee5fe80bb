application:
  port: 8080
  pdd:
    domain: https://gw-api.pinduoduo.com/api/router
    api_good_search: pdd.ddk.goods.search
    api_gen_short_url: pdd.ddk.goods.zs.unit.url.gen
    api_order_detail: pdd.ddk.order.detail.get
  jd:
    app_key: SecretString,
    app_secret: SecretString,
    domain: String,
    api_good_search: String,
    api_gen_short_url: String,
  wechat:
    app_id: 11
    app_secret: 2222,
    token: 123

log:
  log_dir: logs
  targets:
    - kind: stdout
      level: info
    - kind: file
      level: info
      filename: info.log
      rotation: daily
    - kind: file
      level: error
      filename: error.log
      rotation: daily
db:
  host: 127.0.0.1
  port: 5432
  database: kuai_saver
  username: postgres
  password: password
